plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}



def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}



// def keystoreProperties = new Properties()
// def keystorePropertiesFile = rootProject.file('key.properties')
// if (keystorePropertiesFile.exists()) {
//     keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
// }

android {
    compileSdkVersion 34

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }

    defaultConfig {
        applicationId "com.elec.aslaa"
        minSdkVersion 23
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }
    // 在这里添加 sourceCompatibility 和 targetCompatibility
    compileOptions {
       sourceCompatibility = '11'
        targetCompatibility = '11'
    }

    signingConfigs {
        config {
          keyAlias 'key0'
          keyPassword 'shugao19880814'
          storeFile file('../wan.jks')
          storePassword 'shugao19880814'
        }
        // release {
        //     keyAlias keystoreProperties['keyAlias']
        //     keyPassword keystoreProperties['keyPassword']
        //     storeFile file(keystoreProperties['storeFile'])
        //     storePassword keystoreProperties['storePassword']
        // }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.config
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation 'com.google.firebase:firebase-analytics:19.0.0'

    implementation platform('com.google.firebase:firebase-bom:28.4.0')
    implementation 'com.google.firebase:firebase-auth'
    implementation 'com.google.firebase:firebase-database'
    implementation 'com.google.firebase:firebase-auth:21.0.1'
    implementation 'com.google.firebase:firebase-firestore:23.0.3'
    implementation 'com.google.firebase:firebase-messaging:22.0.0'
    implementation 'com.google.firebase:firebase-core:21.0.0'
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}
