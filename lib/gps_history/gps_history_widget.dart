import 'dart:async';

import 'package:aslaa/constant.dart';
// import 'package:aslaa/flutter_flow/flutter_flow_google_map.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/providers/app_provider.dart';
// import 'package:google_maps_routes/google_maps_routes.dart';
import 'package:osrm/osrm.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:aslaa/flutter_flow/flutter_flow_drop_down.dart';
import 'package:aslaa/flutter_flow/flutter_flow_icon_button.dart';
import 'package:aslaa/flutter_flow/flutter_flow_theme.dart';
import 'package:aslaa/flutter_flow/flutter_flow_util.dart'
    show DateFormat, jsonDecode, jsonEncode, FFLocalizations;
import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import 'package:aslaa/flutter_flow/flutter_flow_model.dart';
import 'package:custom_date_range_picker/custom_date_range_picker.dart';
import 'package:http/http.dart' as http;
import 'gps_history_model.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
export 'package:google_maps_flutter/google_maps_flutter.dart' hide LatLng;
export '../flutter_flow/lat_lng.dart' hide LatLng;
import 'package:custom_marker/marker_icon.dart';
import 'package:flutter_animarker/flutter_map_marker_animation.dart';
// import 'package:google_maps_cluster_manager/google_maps_cluster_manager.dart';  // Commented out due to conflict

const ksMarkerId = MarkerId('gps_historyid');
const kLocations = [
  LatLng(18.488101, -69.957995),
  LatLng(18.489210, -69.952459),
  LatLng(18.487307, -69.952759),
  LatLng(18.487308, -69.952759),
];

class GpsHistoryWidget extends StatefulWidget {
  const GpsHistoryWidget({Key? key}) : super(key: key);

  @override
  _GpsHistoryWidgetState createState() => _GpsHistoryWidgetState();
}

class _GpsHistoryWidgetState extends State<GpsHistoryWidget> {
  late GpsHistoryModel _model;
  DateTime? startDate = DateTime.now();
  DateTime? endDate = DateTime.now().add(new Duration(days: 1));
  final scaffoldKey = GlobalKey<ScaffoldState>();
  List<LatLng> points = [];
  Completer<GoogleMapController> _controller = Completer<GoogleMapController>();
  LatLng center = LatLng(47.91672, 106.90246);
  final CameraPosition _parisCameraPosition =
      CameraPosition(target: LatLng(48.856613, 2.352222), zoom: 12.0);
  // MapsRoutes route = new MapsRoutes();

  final osrm = Osrm();
  List<OsrmCoordinate> osrmPoints = [];

  StreamController<LatLng> streamCtrl = StreamController();
  //用做添加事件的入口
  StreamSink get pointsS => streamCtrl.sink;
  //Stream用来监听数据
  Stream get pointsStream => streamCtrl.stream;
  //Stream的订阅对象
  StreamSubscription? subscription;

  @override
  void initState() {
    super.initState();
    // _manager = _initClusterManager();
    setupInitData();
    _model = createModel(context, () => GpsHistoryModel());
  }

  BitmapDescriptor? carIcon;
  // late ClusterManager _manager;  // Commented out due to conflict
  List<Place> items = [
    // for (int i = 0; i < 10; i++)
    //   Place(
    //       name: 'Place $i',
    //       latLng: LatLng(48.848200 + i * 0.001, 2.319124 + i * 0.001)),
    // for (int i = 0; i < 10; i++)
    //   Place(
    //       name: 'Restaurant $i',
    //       isClosed: i % 2 == 0,
    //       latLng: LatLng(48.858265 - i * 0.001, 2.350107 + i * 0.001)),
    // for (int i = 0; i < 10; i++)
    //   Place(
    //       name: 'Bar $i',
    //       latLng: LatLng(48.858265 + i * 0.01, 2.350107 - i * 0.01)),
    // for (int i = 0; i < 10; i++)
    //   Place(
    //       name: 'Hotel $i',
    //       latLng: LatLng(48.858265 - i * 0.1, 2.350107 - i * 0.01)),
    // for (int i = 0; i < 10; i++)
    //   Place(
    //       name: 'Test $i',
    //       latLng: LatLng(66.160507 + i * 0.1, -153.369141 + i * 0.1)),
    // for (int i = 0; i < 10; i++)
    //   Place(
    //       name: 'Test2 $i',
    //       latLng: LatLng(-36.848461 + i * 1, 169.763336 + i * 1)),
  ];
  setupInitData() async {
    try {
      carIcon = await MarkerIcon.pictureAsset(
          assetPath: 'assets/images/car-body.png', width: 120, height: 120);
      print("setupInitData: carIcon loaded successfully");
    } catch (e) {
      print("setupInitData: Error loading carIcon: $e");
      // Use default marker as fallback
      carIcon = BitmapDescriptor.defaultMarker;
    }
  }

  // ClusterManager _initClusterManager() {
  //   return ClusterManager<Place>(items, _updateMarkers,
  //       markerBuilder: _markerBuilder);
  // }

  // Future<Marker> Function(dynamic) get _markerBuilder => (cluster) async {
  //       if (carIcon == null) {
  //         carIcon = await MarkerIcon.pictureAsset(
  //             assetPath: 'assets/images/car-body.png', width: 120, height: 120);
  //         print("carIcon - $carIcon");
  //       }
  //       return Marker(
  //         markerId: MarkerId(cluster.getId()),
  //         position: cluster.location,
  //         onTap: () {
  //           print('---- $cluster');
  //           cluster.items.forEach((p) => print(p));
  //         },
  //         icon: carIcon!,
  //       );
  //     };

  // void _updateMarkers(Set<Marker> markers) {
  //   print('Updated ${markers.length} markers');
  //   setState(() {
  //     List<LatLng> tpoints = [];
  //     markers.forEach((log) async {
  //       try {
  //         if (log.position.latitude > 0 && log.position.latitude > 0) {
  //           tpoints.add(LatLng(log.position.latitude, log.position.longitude));

  //           if (carIcon == null) {
  //             carIcon = await MarkerIcon.pictureAsset(
  //                 assetPath: 'assets/images/car-body.png',
  //                 width: 120,
  //                 height: 120);
  //             print("carIcon - $carIcon");
  //           }
  //           var marker = RippleMarker(
  //               markerId: log.markerId,
  //               position: LatLng(log.position.latitude, log.position.longitude),
  //               // rotation: rorate,
  //               ripple: true,
  //               anchor: const Offset(0.5, 0.0),
  //               icon: carIcon,
  //               onTap: () async {
  //                 print(
  //                     'Tapped! $LatLng(log.position.latitude, log.position.longitude)');
  //               });
  //           this.markers[ksMarkerId] = marker;
  //           // print("marker - $marker");
  //           // this.markers.add(marker);
  //         }
  //       } catch (err) {
  //         debugPrint('$err');
  //       }
  //     });

  //     // points = tpoints;
  //     // var polys = Set<Polyline>();
  //     // var poly = Polyline(polylineId: PolylineId("PolylineId"),patterns: <PatternItem>[PatternItem.dash(5), PatternItem.gap(5)],jointType: JointType.mitered,geodesic:true,points: points,width: 3,color: Colors.green, startCap: Cap.roundCap,
  //     //     endCap: Cap.roundCap);
  //     // polys.add(poly);
  //     // routes = polys;
  //     // points.forEach((value) => newLocationUpdate(value));
  //   });
  // }

  final markers = <MarkerId, Marker>{};
  double initialZoom = 15;
  // Set<Marker> markers = Set();
  // List<OsrmRoute>? routes;
  @override
  void dispose() async {
    _model.dispose();
    // await positionStream.cancel();
    var controller = await _controller.future;
    controller.dispose();
    super.dispose();
  }

  var josnInfo = {
    "success": true,
    "logs": [
      {
        "_id": "6569bd804ef74e1b9356b2bb",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"0 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"0 N\",\"hum\":22,\"sta\":0,\"volt\":12,\"Speed\":0,\"temp\":-4,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-01T11:03:28.694Z",
        "__v": 0
      },
      {
        "_id": "6569fe664ef74e1b93570640",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.82938 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.87232 N\",\"hum\":9,\"sta\":0,\"volt\":15,\"Speed\":5,\"temp\":23,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-01T15:40:22.660Z",
        "__v": 0
      },
      {
        "_id": "6569fe804ef74e1b93570667",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.82636 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.87170 N\",\"hum\":8,\"sta\":0,\"volt\":15,\"Speed\":44,\"temp\":23,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-01T15:40:48.017Z",
        "__v": 0
      },
      {
        "_id": "656a019f8e8059021de3da21",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.78192 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.84896 N\",\"hum\":5,\"sta\":0,\"volt\":15,\"Speed\":3,\"temp\":26,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-01T15:54:07.406Z",
        "__v": 0
      },
      {
        "_id": "656a04228e8059021de3dbc8",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.84603 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.87532 N\",\"hum\":1,\"sta\":0,\"volt\":15,\"Speed\":55,\"temp\":31,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-01T16:04:50.810Z",
        "__v": 0
      },
      {
        "_id": "656a04288e8059021de3dbd4",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.84726 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.87562 N\",\"hum\":1,\"sta\":0,\"volt\":15,\"Speed\":59,\"temp\":31,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-01T16:04:56.450Z",
        "__v": 0
      },
      {
        "_id": "656a05048e8059021de3dc79",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.87609 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.89503 N\",\"hum\":1,\"sta\":0,\"volt\":15,\"Speed\":1,\"temp\":32,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-01T16:08:36.077Z",
        "__v": 0
      },
      {
        "_id": "656a136f8e8059021de3e58d",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"0 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"0 N\",\"hum\":9,\"sta\":0,\"volt\":13,\"Speed\":0,\"temp\":17,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-01T17:10:07.666Z",
        "__v": 0
      },
      {
        "_id": "656ac9f98e8059021de4a2d1",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.92727 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.91147 N\",\"hum\":26,\"sta\":0,\"volt\":12,\"Speed\":1,\"temp\":-2,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-02T06:08:57.859Z",
        "__v": 0
      },
      {
        "_id": "656b14b8a001c8584aea7624",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"0 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"0 N\",\"hum\":26,\"sta\":0,\"volt\":12,\"Speed\":0,\"temp\":-2,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-02T11:27:52.572Z",
        "__v": 0
      },
      {
        "_id": "656d54e266cd8dbc924c0431",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.92841 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.91387 N\",\"hum\":29,\"sta\":0,\"volt\":12,\"Speed\":0,\"temp\":-4,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-04T04:26:10.530Z",
        "__v": 0
      },
      {
        "_id": "656d660766cd8dbc924c1b3a",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"0 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"0 N\",\"hum\":9,\"sta\":0,\"volt\":13,\"Speed\":0,\"temp\":27,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-04T05:39:19.953Z",
        "__v": 0
      },
      {
        "_id": "656d880766cd8dbc924c4096",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.89500 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.92571 N\",\"hum\":3,\"sta\":0,\"volt\":15,\"Speed\":0,\"temp\":32,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-04T08:04:24.000Z",
        "__v": 0
      },
      {
        "_id": "656f039266cd8dbc924e5aeb",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"0 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"0 N\",\"hum\":27,\"sta\":0,\"volt\":13,\"Speed\":0,\"temp\":-1,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-05T11:03:46.173Z",
        "__v": 0
      },
      {
        "_id": "656f219866cd8dbc924e846c",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.92973 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.90970 N\",\"hum\":27,\"sta\":0,\"volt\":13,\"Speed\":0,\"temp\":-2,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-05T13:11:52.617Z",
        "__v": 0
      },
      {
        "_id": "656fcec666cd8dbc924f5558",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.93324 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.91402 N\",\"hum\":25,\"sta\":0,\"volt\":12,\"Speed\":5,\"temp\":-11,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-06T01:30:46.643Z",
        "__v": 0
      },
      {
        "_id": "656fda3366cd8dbc924f6a34",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"0 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"0 N\",\"hum\":25,\"sta\":0,\"volt\":12,\"Speed\":0,\"temp\":-10,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-06T02:19:31.502Z",
        "__v": 0
      },
      {
        "_id": "6571827966cd8dbc9251fa90",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.94688 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.90893 N\",\"hum\":22,\"sta\":0,\"volt\":13,\"Speed\":27,\"temp\":18,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-07T08:29:45.880Z",
        "__v": 0
      },
      {
        "_id": "6571887866cd8dbc92520415",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.92914 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.91122 N\",\"hum\":20,\"sta\":0,\"volt\":15,\"Speed\":5,\"temp\":25,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-07T08:55:20.611Z",
        "__v": 0
      },
      {
        "_id": "65719b6566cd8dbc92523aec",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"0 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"0 N\",\"hum\":10,\"sta\":0,\"volt\":13,\"Speed\":0,\"temp\":35,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-07T10:16:05.424Z",
        "__v": 0
      },
      {
        "_id": "6571b6c192b9275e5d92c7ec",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.81651 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.92633 N\",\"hum\":15,\"sta\":0,\"volt\":15,\"Speed\":0,\"temp\":28,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-07T12:12:49.558Z",
        "__v": 0
      },
      {
        "_id": "6571c5c292b9275e5d92d93f",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.88168 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.90428 N\",\"hum\":10,\"sta\":0,\"volt\":13,\"Speed\":0,\"temp\":27,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-07T13:16:50.831Z",
        "__v": 0
      },
      {
        "_id": "6572695492b9275e5d9382c1",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.92726 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.93629 N\",\"hum\":13,\"sta\":0,\"volt\":13,\"Speed\":0,\"temp\":5,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-08T00:54:44.717Z",
        "__v": 0
      },
      {
        "_id": "6572bd6892b9275e5d9411f2",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"107.79 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.106 N\",\"hum\":28,\"sta\":1,\"volt\":14,\"Speed\":10,\"temp\":26,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-08T06:53:28.315Z",
        "__v": 0
      },
      {
        "_id": "6572bda392b9275e5d941288",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.92724 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.93627 N\",\"hum\":20,\"sta\":0,\"volt\":12,\"Speed\":0,\"temp\":-6,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-08T06:54:27.778Z",
        "__v": 0
      },
      {
        "_id": "6572bdb092b9275e5d9412b2",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"107.79 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.106 N\",\"hum\":28,\"sta\":1,\"volt\":14,\"Speed\":10,\"temp\":26,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-08T06:54:40.008Z",
        "__v": 0
      },
      {
        "_id": "6572be3892b9275e5d94138e",
        "deviceNumber": "864865062702486",
        "payload":
            "{\"Lon\":\"106.92728 E\",\"rel2\":0,\"rel1\":0,\"Lat\":\"47.93621 N\",\"hum\":20,\"sta\":0,\"volt\":14,\"Speed\":0,\"temp\":-6,\"motion\":0,\"light\":0}",
        "createdAt": "2023-12-08T06:56:56.278Z",
        "__v": 0
      }
    ]
  };

  Stream? stream;
  bool loading = false;
  Set<Polyline> routes = {};
  Future _loadGpsData() async {
    setState(() {
      loading = true;
    });
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      String? token = _prefs.getString('token');
      print('get-by-date: ${token}');
      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };
      Map<String, dynamic> bodys = {
        "deviceNumber": _model.dropDownValue ?? "861551054373154",
        "from": DateFormat('yyyy-MM-dd').format(startDate!),
        "to": DateFormat('yyyy-MM-dd').format(endDate!),
      };
      print('get-by-date: ${bodys}');
      var response = await http.post(Uri.parse('$API_HOST/api/log/get-by-date'),
          headers: headers, body: jsonEncode(bodys));
      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        // this.josnInfo;

        if (json['success'] != null && json['success']) {
          List<dynamic> data = json['logs'];
          for (int i = 0; i < data.length; i++) {
            var log = data[i];
            String? payload = log['payload'];
            if (payload != null &&
                payload.contains("Lon") &&
                payload.contains("Lat")) {
              try {
                Map<String, dynamic> status = jsonDecode(payload);
                var lat = double.parse(status['Lat'].split(' ')[0]);
                var lon = double.parse(status['Lon'].split(' ')[0]);
                if (lat > 0 && lon > 0) {
                  newLocationUpdate(LatLng(lat, lon));
                  OsrmCoordinate p = (lon, lat);
                  osrmPoints.add(p);
                  // points.add(LatLng(lat, lon));
                  setState(() {});
                  // items.add(Place(name: "PlaceId $i", latLng: LatLng(lat, lon)));
                }
              } catch (err) {
                debugPrint('$err');
              }
            }
          }
          // data.forEach((log) {
          //   String? payload = log['payload'];
          //   if (payload != null &&
          //       payload.contains("Lon") &&
          //       payload.contains("Lat")) {
          //     try {
          //       Map<String, dynamic> status = jsonDecode(payload);
          //       var lat =  double.parse(status['Lat'].split(' ')[0]);
          //       var lon =  double.parse(status['Lon'].split(' ')[0]);
          //       if (lat > 0 && lon > 0) {
          //       points.add(LatLng(lat, lon));
          //       items.add(Place(name: "PlaceId ", latLng: LatLng(lat, lon)));
          //       }
          //     } catch (err) {
          //       debugPrint('$err');
          //     }
          //   }
          // });

          // setState(()  {
          // var poly = Polyline(polylineId: PolylineId("PolylineId"),patterns: <PatternItem>[PatternItem.dash(5), PatternItem.gap(5)],jointType: JointType.mitered,geodesic:true,points: points,width: 5,color: Colors.green, startCap: Cap.roundCap,
          //     endCap: Cap.roundCap);
          // routes.add(poly);
          // _manager = _initClusterManager();  // Commented out due to conflict

          // await route.drawRoute(points, 'Test routes',
          //     Color.fromRGBO(130, 78, 210, 1.0), "AIzaSyAOufjq8MpN-8eipkQHe6J7Mapit66FJM8",
          //     travelMode: TravelModes.driving);
// get the route
          final options = RouteRequest(
            coordinates: osrmPoints,
            // geometries: OsrmGeometries.geojson,
            overview: OsrmOverview.full,
            // alternatives: OsrmAlternative.number(2),
            // annotations: OsrmAnnotation.true_,
            // steps: true,
          );
          // });

          final route = await osrm.route(options);
          // var distance = route.routes.first.distance!;
          // var duration = route.routes.first.duration!;
          points =
              route.routes.first.geometry!.lineString!.coordinates.map((e) {
            var location = e.toLocation();
            return LatLng(location.lat, location.lng);
          }).toList();
          setState(() {
            var poly = Polyline(
                polylineId: PolylineId("PolylineId"),
                patterns: <PatternItem>[
                  PatternItem.dash(5),
                  PatternItem.gap(5)
                ],
                jointType: JointType.mitered,
                geodesic: true,
                points: points,
                width: 5,
                color: Colors.green,
                startCap: Cap.roundCap,
                endCap: Cap.roundCap);
            routes.add(poly);

            stream =
                Stream.periodic(Duration(seconds: 2), (count) => points[count])
                    .take(points.length);
          });
        } else {
          print('Error: ${json['message']}');
        }
      } else {
        print('Server error');
      }
    } catch (e) {
      debugPrint('错误$e');
    } finally {
      setState(() {
        loading = false;
      });
    }
  }

  Future<void> onStopover(LatLng latLng) async {
    print("onStopover --- ");
    if (!_controller.isCompleted) return;
  }

  void newLocationUpdate(LatLng latLng) async {
//     double rorate = atan2(currentMapCenter.latitude - latLng.latitude, currentMapCenter.longitude - latLng.longitude);
//     double angle = rorate / pi * 180;
// print("rorate ${rorate} angle ${angle}");
//     double rorate = 0.0;
//     var j = this;
//     var x = latLng.longitude-center.longitude;//lng-经度，lat-纬度
//     var y = latLng.latitude-center.latitude;
//     if(x==0){
//       rorate = 0.0;
//     }
//     if(x>0){
//       var z=sqrt(x*x+y*y);
//       var jiaodu = (asin(y/z)/pi*180).round();//最终角度
//       if(jiaodu>=0){
//         rorate = 180.0-jiaodu;
//       }else{
//         rorate = 180.0 + jiaodu.abs();
//       }
//     }
//     if(x<0){
//       var z= sqrt(x*x+y*y);
//       var jiaodu=(asin(y/z)/pi*180).round();//最终角度
//       if(jiaodu>=0){
//         rorate = jiaodu.toDouble();
//       }else{
//         rorate = 360.0 - jiaodu.abs();
//       }
//     }

    // print("rorate  - ${rorate}");
    LatLng tlatLng = LatLng(double.parse(latLng.latitude.toStringAsFixed(6)),
        double.parse(latLng.longitude.toStringAsFixed(6)));
    // Ensure carIcon is loaded before creating marker
    if (carIcon == null) {
      try {
        carIcon = await MarkerIcon.pictureAsset(
            assetPath: 'assets/images/car-body.png', width: 120, height: 120);
        print("carIcon loaded - $carIcon");
      } catch (e) {
        print("Error loading carIcon: $e");
        // Use default marker if custom icon fails
        carIcon = BitmapDescriptor.defaultMarker;
      }
    }

    // Only create marker if carIcon is available
    if (carIcon != null) {
      var marker = RippleMarker(
          markerId: ksMarkerId,
          position: tlatLng,
          // rotation: rorate,
          ripple: false,
          anchor: const Offset(0.5, 0.0),
          icon: carIcon!,
          onTap: () async {
            print('Tapped! $tlatLng');
          });

      print("marker created - $marker");
      setState(() {
        // center = tlatLng;
        markers[ksMarkerId] = marker;
      });
    } else {
      print("carIcon is null, skipping marker creation");
    }
  }

  Widget mapWidget() {
    print("mapWidget ----");
    // return StreamBuilder(
    //     stream: pointsStream,
    //     builder: (BuildContext context, AsyncSnapshot snapshot) {
    return Animarker(
        curve: Curves.bounceOut,
        isActiveTrip: true,
        rippleRadius: 0.25,
        useRotation: true,
        zoom: initialZoom,
        onStopover: onStopover,
        duration: Duration(milliseconds: 2000),
        mapId: _controller.future.then<int>((value) => value.mapId),
        //Grab Google Map Id
        markers: markers.values.toSet(),
        child: GoogleMap(
            // initialCameraPosition: _parisCameraPosition,

            initialCameraPosition: CameraPosition(
              zoom: initialZoom,
              target: center,
            ),
            polylines: routes,
            onCameraMove: (position) {
              center = position.target;
              initialZoom = position.zoom;
            },
            mapType: MapType.normal,
            style: googleMapStyleStrings[GoogleMapStyle.standard],
            // onCameraMove: _manager.onCameraMove,  // Commented out due to cluster manager removal
            onCameraIdle: () {
              // setState(() {
              //   center = center;
              // });
            },
            zoomGesturesEnabled: true,
            zoomControlsEnabled: true,
            myLocationEnabled: true,
            compassEnabled: false,
            mapToolbarEnabled: false,
            trafficEnabled: false,
            // markers: markers.values.toSet(),
            onMapCreated: (GoogleMapController controller) async {
              if (!_controller.isCompleted) {
                _controller.complete(controller);
              }
              // Map style is now set via the GoogleMap widget's style property
            }));
    // });
  }

  @override
  Widget build(BuildContext context) {
    AppProvider authProvider = Provider.of<AppProvider>(context);
    User? user = authProvider.authClient;
    return Scaffold(
      key: scaffoldKey,
      resizeToAvoidBottomInset: false,
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(60),
        child: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          automaticallyImplyLeading: false,
          title: Text(
            FFLocalizations.of(context).getText(
              '6g39sgd4' /* GPS HISTORY */,
            ),
            style: FlutterFlowTheme.of(context).title2.override(
                  fontFamily: 'Roboto',
                  color: FlutterFlowTheme.of(context).primaryText,
                  fontSize: 22,
                ),
          ),
          actions: [],
          centerTitle: false,
          elevation: 0,
        ),
      ),
      body: StreamBuilder(
          stream: pointsStream,
          builder: (BuildContext context, AsyncSnapshot snapshot) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(0, 12, 0, 12),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            FlutterFlowIconButton(
                              borderColor: Colors.transparent,
                              borderRadius: 20,
                              borderWidth: 1,
                              buttonSize: 40,
                              icon: Icon(
                                Icons.home,
                                color: FlutterFlowTheme.of(context).primaryText,
                                size: 20,
                              ),
                              onPressed: () async {
                                context.pushNamed('dashboard');
                              },
                            ),
                            FlutterFlowDropDown<String>(
                              options: user?.devices
                                      ?.map((e) => e.deviceNumber)
                                      .toList() ??
                                  [],
                              optionLabels: user?.devices
                                      ?.map((e) => (e.deviceName == ""
                                          ? e.deviceNumber
                                          : e.deviceName))
                                      .toList() ??
                                  [],
                              onChanged: (val) =>
                                  setState(() => _model.dropDownValue = val),
                              valueOption: _model.dropDownValue,
                              width: 240,
                              height: 50,
                              textStyle: FlutterFlowTheme.of(context)
                                  .bodyText1
                                  .override(
                                    fontFamily: 'Roboto',
                                    color: FlutterFlowTheme.of(context)
                                        .primaryText,
                                  ),
                              hintText: FFLocalizations.of(context).getText(
                                'z5aopokj' /* Pleaes select device */,
                              ),
                              fillColor: FlutterFlowTheme.of(context)
                                  .secondaryBackground,
                              elevation: 2,
                              borderColor: Colors.transparent,
                              borderWidth: 0,
                              borderRadius: 8,
                              margin:
                                  EdgeInsetsDirectional.fromSTEB(12, 4, 12, 4),
                              hidesUnderline: true,
                            ),
                          ],
                        ),
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Generated code for this dateRangeRow Widget...
                          Padding(
                            padding:
                                EdgeInsetsDirectional.fromSTEB(24, 12, 24, 12),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                FlutterFlowIconButton(
                                  borderColor: Colors.transparent,
                                  borderRadius: 24,
                                  borderWidth: 1,
                                  buttonSize: 48,
                                  icon: Icon(
                                    Icons.calendar_today_outlined,
                                    color: FlutterFlowTheme.of(context)
                                        .primaryText,
                                    size: 24,
                                  ),
                                  onPressed: () {
                                    showCustomDateRangePicker(
                                      context,
                                      dismissible: true,
                                      minimumDate: DateTime.now()
                                          .add(const Duration(days: -30)),
                                      maximumDate: DateTime.now()
                                          .add(const Duration(days: 30)),
                                      endDate: endDate,
                                      startDate: startDate,
                                      onApplyClick: (start, end) {
                                        setState(() {
                                          endDate = end;
                                          startDate = start;
                                        });
                                      },
                                      onCancelClick: () {
                                        setState(() {
                                          startDate = DateTime.now();
                                          endDate = DateTime.now()
                                              .add(new Duration(days: 1));
                                        });
                                      },
                                      // kun91 - 变动
                                      backgroundColor: Colors.white,
                                      primaryColor: Colors.lightBlueAccent,
                                    );
                                  },
                                ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      8, 0, 8, 0),
                                  child: Text(
                                    '${DateFormat('yMd').format(startDate!)}-${DateFormat('yMd').format(endDate!)}',
                                    style:
                                        FlutterFlowTheme.of(context).bodyText1,
                                  ),
                                ),
                                FlutterFlowIconButton(
                                  borderColor: Colors.transparent,
                                  borderRadius: 24,
                                  borderWidth: 1,
                                  buttonSize: 48,
                                  icon: Icon(
                                    Icons.refresh,
                                    color: FlutterFlowTheme.of(context)
                                        .primaryText,
                                    size: 24,
                                  ),
                                  onPressed: () async {
                                    await _loadGpsData();
                                    print('IconButton pressed ...');
                                    // Future.delayed(Duration(microseconds: 3500), () async {
                                    //   points.forEach((value) => newLocationUpdate(LatLng(value.latitude,value.longitude)));
                                    //   print("points --- ${points}");
                                    // });
                                    stream?.forEach((value) =>
                                        newLocationUpdate(LatLng(
                                            value.latitude, value.longitude)));
                                  },
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ],
                  ),
                  Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 0),
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height * 0.6,
                      decoration: BoxDecoration(
                        color: FlutterFlowTheme.of(context).secondaryBackground,
                      ),
                      child:
                          // loading
                          //     ? Padding(
                          //         padding: EdgeInsetsDirectional.fromSTEB(60, 60, 60, 60),
                          //         child: Row(
                          //           children: [
                          //             CircularProgressIndicator(
                          //               strokeWidth: 10,
                          //             ),
                          //           ],
                          //           mainAxisAlignment: MainAxisAlignment.center,
                          //         ))
                          //     :
                          mapWidget(),
                    ),
                  ),
                ],
              ),
            );
          }),
    );
  }

  Map<GoogleMapStyle, String> googleMapStyleStrings = {
    GoogleMapStyle.standard: '[]',
    GoogleMapStyle.silver:
        r'[{"elementType":"geometry","stylers":[{"color":"#f5f5f5"}]},{"elementType":"labels.icon","stylers":[{"visibility":"off"}]},{"elementType":"labels.text.fill","stylers":[{"color":"#616161"}]},{"elementType":"labels.text.stroke","stylers":[{"color":"#f5f5f5"}]},{"featureType":"administrative.land_parcel","elementType":"labels.text.fill","stylers":[{"color":"#bdbdbd"}]},{"featureType":"poi","elementType":"geometry","stylers":[{"color":"#eeeeee"}]},{"featureType":"poi","elementType":"labels.text.fill","stylers":[{"color":"#757575"}]},{"featureType":"poi.park","elementType":"geometry","stylers":[{"color":"#e5e5e5"}]},{"featureType":"poi.park","elementType":"labels.text.fill","stylers":[{"color":"#9e9e9e"}]},{"featureType":"road","elementType":"geometry","stylers":[{"color":"#ffffff"}]},{"featureType":"road.arterial","elementType":"labels.text.fill","stylers":[{"color":"#757575"}]},{"featureType":"road.highway","elementType":"geometry","stylers":[{"color":"#dadada"}]},{"featureType":"road.highway","elementType":"labels.text.fill","stylers":[{"color":"#616161"}]},{"featureType":"road.local","elementType":"labels.text.fill","stylers":[{"color":"#9e9e9e"}]},{"featureType":"transit.line","elementType":"geometry","stylers":[{"color":"#e5e5e5"}]},{"featureType":"transit.station","elementType":"geometry","stylers":[{"color":"#eeeeee"}]},{"featureType":"water","elementType":"geometry","stylers":[{"color":"#c9c9c9"}]},{"featureType":"water","elementType":"labels.text.fill","stylers":[{"color":"#9e9e9e"}]}]',
    GoogleMapStyle.retro:
        r'[{"elementType":"geometry","stylers":[{"color":"#ebe3cd"}]},{"elementType":"labels.text.fill","stylers":[{"color":"#523735"}]},{"elementType":"labels.text.stroke","stylers":[{"color":"#f5f1e6"}]},{"featureType":"administrative","elementType":"geometry.stroke","stylers":[{"color":"#c9b2a6"}]},{"featureType":"administrative.land_parcel","elementType":"geometry.stroke","stylers":[{"color":"#dcd2be"}]},{"featureType":"administrative.land_parcel","elementType":"labels.text.fill","stylers":[{"color":"#ae9e90"}]},{"featureType":"landscape.natural","elementType":"geometry","stylers":[{"color":"#dfd2ae"}]},{"featureType":"poi","elementType":"geometry","stylers":[{"color":"#dfd2ae"}]},{"featureType":"poi","elementType":"labels.text.fill","stylers":[{"color":"#93817c"}]},{"featureType":"poi.park","elementType":"geometry.fill","stylers":[{"color":"#a5b076"}]},{"featureType":"poi.park","elementType":"labels.text.fill","stylers":[{"color":"#447530"}]},{"featureType":"road","elementType":"geometry","stylers":[{"color":"#f5f1e6"}]},{"featureType":"road.arterial","elementType":"geometry","stylers":[{"color":"#fdfcf8"}]},{"featureType":"road.highway","elementType":"geometry","stylers":[{"color":"#f8c967"}]},{"featureType":"road.highway","elementType":"geometry.stroke","stylers":[{"color":"#e9bc62"}]},{"featureType":"road.highway.controlled_access","elementType":"geometry","stylers":[{"color":"#e98d58"}]},{"featureType":"road.highway.controlled_access","elementType":"geometry.stroke","stylers":[{"color":"#db8555"}]},{"featureType":"road.local","elementType":"labels.text.fill","stylers":[{"color":"#806b63"}]},{"featureType":"transit.line","elementType":"geometry","stylers":[{"color":"#dfd2ae"}]},{"featureType":"transit.line","elementType":"labels.text.fill","stylers":[{"color":"#8f7d77"}]},{"featureType":"transit.line","elementType":"labels.text.stroke","stylers":[{"color":"#ebe3cd"}]},{"featureType":"transit.station","elementType":"geometry","stylers":[{"color":"#dfd2ae"}]},{"featureType":"water","elementType":"geometry.fill","stylers":[{"color":"#b9d3c2"}]},{"featureType":"water","elementType":"labels.text.fill","stylers":[{"color":"#92998d"}]}]',
    GoogleMapStyle.dark:
        r'[{"elementType":"geometry","stylers":[{"color":"#212121"}]},{"elementType":"labels.icon","stylers":[{"visibility":"off"}]},{"elementType":"labels.text.fill","stylers":[{"color":"#757575"}]},{"elementType":"labels.text.stroke","stylers":[{"color":"#212121"}]},{"featureType":"administrative","elementType":"geometry","stylers":[{"color":"#757575"}]},{"featureType":"administrative.country","elementType":"labels.text.fill","stylers":[{"color":"#9e9e9e"}]},{"featureType":"administrative.land_parcel","stylers":[{"visibility":"off"}]},{"featureType":"administrative.locality","elementType":"labels.text.fill","stylers":[{"color":"#bdbdbd"}]},{"featureType":"poi","elementType":"labels.text.fill","stylers":[{"color":"#757575"}]},{"featureType":"poi.park","elementType":"geometry","stylers":[{"color":"#181818"}]},{"featureType":"poi.park","elementType":"labels.text.fill","stylers":[{"color":"#616161"}]},{"featureType":"poi.park","elementType":"labels.text.stroke","stylers":[{"color":"#1b1b1b"}]},{"featureType":"road","elementType":"geometry.fill","stylers":[{"color":"#2c2c2c"}]},{"featureType":"road","elementType":"labels.text.fill","stylers":[{"color":"#8a8a8a"}]},{"featureType":"road.arterial","elementType":"geometry","stylers":[{"color":"#373737"}]},{"featureType":"road.highway","elementType":"geometry","stylers":[{"color":"#3c3c3c"}]},{"featureType":"road.highway.controlled_access","elementType":"geometry","stylers":[{"color":"#4e4e4e"}]},{"featureType":"road.local","elementType":"labels.text.fill","stylers":[{"color":"#616161"}]},{"featureType":"transit","elementType":"labels.text.fill","stylers":[{"color":"#757575"}]},{"featureType":"water","elementType":"geometry","stylers":[{"color":"#000000"}]},{"featureType":"water","elementType":"labels.text.fill","stylers":[{"color":"#3d3d3d"}]}]',
    GoogleMapStyle.night:
        r'[{"elementType":"geometry","stylers":[{"color":"#242f3e"}]},{"elementType":"labels.text.fill","stylers":[{"color":"#746855"}]},{"elementType":"labels.text.stroke","stylers":[{"color":"#242f3e"}]},{"featureType":"administrative.locality","elementType":"labels.text.fill","stylers":[{"color":"#d59563"}]},{"featureType":"poi","elementType":"labels.text.fill","stylers":[{"color":"#d59563"}]},{"featureType":"poi.park","elementType":"geometry","stylers":[{"color":"#263c3f"}]},{"featureType":"poi.park","elementType":"labels.text.fill","stylers":[{"color":"#6b9a76"}]},{"featureType":"road","elementType":"geometry","stylers":[{"color":"#38414e"}]},{"featureType":"road","elementType":"geometry.stroke","stylers":[{"color":"#212a37"}]},{"featureType":"road","elementType":"labels.text.fill","stylers":[{"color":"#9ca5b3"}]},{"featureType":"road.highway","elementType":"geometry","stylers":[{"color":"#746855"}]},{"featureType":"road.highway","elementType":"geometry.stroke","stylers":[{"color":"#1f2835"}]},{"featureType":"road.highway","elementType":"labels.text.fill","stylers":[{"color":"#f3d19c"}]},{"featureType":"transit","elementType":"geometry","stylers":[{"color":"#2f3948"}]},{"featureType":"transit.station","elementType":"labels.text.fill","stylers":[{"color":"#d59563"}]},{"featureType":"water","elementType":"geometry","stylers":[{"color":"#17263c"}]},{"featureType":"water","elementType":"labels.text.fill","stylers":[{"color":"#515c6d"}]},{"featureType":"water","elementType":"labels.text.stroke","stylers":[{"color":"#17263c"}]}]',
    GoogleMapStyle.aubergine:
        r'[{"elementType":"geometry","stylers":[{"color":"#1d2c4d"}]},{"elementType":"labels.text.fill","stylers":[{"color":"#8ec3b9"}]},{"elementType":"labels.text.stroke","stylers":[{"color":"#1a3646"}]},{"featureType":"administrative.country","elementType":"geometry.stroke","stylers":[{"color":"#4b6878"}]},{"featureType":"administrative.land_parcel","elementType":"labels.text.fill","stylers":[{"color":"#64779e"}]},{"featureType":"administrative.province","elementType":"geometry.stroke","stylers":[{"color":"#4b6878"}]},{"featureType":"landscape.man_made","elementType":"geometry.stroke","stylers":[{"color":"#334e87"}]},{"featureType":"landscape.natural","elementType":"geometry","stylers":[{"color":"#023e58"}]},{"featureType":"poi","elementType":"geometry","stylers":[{"color":"#283d6a"}]},{"featureType":"poi","elementType":"labels.text.fill","stylers":[{"color":"#6f9ba5"}]},{"featureType":"poi","elementType":"labels.text.stroke","stylers":[{"color":"#1d2c4d"}]},{"featureType":"poi.park","elementType":"geometry.fill","stylers":[{"color":"#023e58"}]},{"featureType":"poi.park","elementType":"labels.text.fill","stylers":[{"color":"#3C7680"}]},{"featureType":"road","elementType":"geometry","stylers":[{"color":"#304a7d"}]},{"featureType":"road","elementType":"labels.text.fill","stylers":[{"color":"#98a5be"}]},{"featureType":"road","elementType":"labels.text.stroke","stylers":[{"color":"#1d2c4d"}]},{"featureType":"road.highway","elementType":"geometry","stylers":[{"color":"#2c6675"}]},{"featureType":"road.highway","elementType":"geometry.stroke","stylers":[{"color":"#255763"}]},{"featureType":"road.highway","elementType":"labels.text.fill","stylers":[{"color":"#b0d5ce"}]},{"featureType":"road.highway","elementType":"labels.text.stroke","stylers":[{"color":"#023e58"}]},{"featureType":"transit","elementType":"labels.text.fill","stylers":[{"color":"#98a5be"}]},{"featureType":"transit","elementType":"labels.text.stroke","stylers":[{"color":"#1d2c4d"}]},{"featureType":"transit.line","elementType":"geometry.fill","stylers":[{"color":"#283d6a"}]},{"featureType":"transit.station","elementType":"geometry","stylers":[{"color":"#3a4762"}]},{"featureType":"water","elementType":"geometry","stylers":[{"color":"#0e1626"}]},{"featureType":"water","elementType":"labels.text.fill","stylers":[{"color":"#4e6d70"}]}]',
  };
}

enum GoogleMapStyle {
  standard,
  silver,
  retro,
  dark,
  night,
  aubergine,
}

class Place {
  final String name;
  final bool isClosed;
  final LatLng latLng;

  Place({required this.name, required this.latLng, this.isClosed = false});

  @override
  String toString() {
    return 'Place $name (closed : $isClosed)';
  }

  LatLng get location => latLng;
}
