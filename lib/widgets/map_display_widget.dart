import 'package:aslaa/flutter_flow/flutter_flow_google_map.dart'
    as ff_google_map;
import 'package:aslaa/flutter_flow/flutter_flow_google_map.dart';
import 'package:aslaa/models/device.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as google_maps;
import 'package:flutter/material.dart';

class MapDisplayWidget extends StatelessWidget {
  final bool showMap;
  final DeviceStatus ds;
  final Completer<google_maps.GoogleMapController> googleMapsController;
  final ff_google_map.LatLng? googleMapsCenter;
  final google_maps.BitmapDescriptor? carIcon;

  const MapDisplayWidget({
    Key? key,
    required this.showMap,
    required this.ds,
    required this.googleMapsController,
    required this.googleMapsCenter,
    required this.carIcon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!showMap) {
      return SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 24),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: Container(
              width: 100,
              height: 600,
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
              ),
              child: FlutterFlowGoogleMap(
                controller: googleMapsController,
                onCameraIdle: (latLng) {},
                initialLocation: googleMapsCenter ??
                    (ds.Lat > 0 && ds.Lon > 0
                        ? ff_google_map.LatLng(
                            ds.Lat.toDouble(), ds.Lon.toDouble())
                        : ff_google_map.LatLng(47.91672, 106.90246)),
                markerColor: GoogleMarkerColor.violet,
                mapType: MapType.normal,
                style: GoogleMapStyle.standard,
                initialZoom: 15,
                allowInteraction: true,
                allowZoom: true,
                showZoomControls: true,
                showLocation: true,
                showCompass: false,
                showMapToolbar: false,
                showTraffic: false,
                centerMapOnMarkerTap: true,
                markers: carIcon != null
                    ? [
                        FlutterFlowMarker(
                          "car-icon",
                          ff_google_map.LatLng(
                              ds.Lat.toDouble(), ds.Lon.toDouble()),
                          carIcon,
                        )
                      ]
                    : [],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
